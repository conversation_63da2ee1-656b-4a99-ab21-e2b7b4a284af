import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:firebase_ui_oauth_google/firebase_ui_oauth_google.dart';
import 'package:firebase_auth/firebase_auth.dart' hide EmailAuthProvider;
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:upshift/pages/main_navigation.dart';
import 'package:upshift/pages/onboarding.dart';
import 'package:upshift/pages/email_verification_page.dart';
import 'package:upshift/pages/legal_web_view_page.dart';
import '../services/firestore.dart';
import '../services/email_verification_service.dart';
import '../services/logging_service.dart';
import '../services/analytics_service.dart';
import '../services/subscription_service.dart';
import '../models/models.dart' as models;
import '../theme/theme.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // If user is logged in
        if (snapshot.hasData) {
          return AuthenticatedUserHandler(user: snapshot.data!);
        }
        // If user is NOT logged in
        else {
          return const FirebaseUIAuthScreen();
        }
      },
    );
  }
}

class AuthenticatedUserHandler extends StatefulWidget {
  final User user;

  const AuthenticatedUserHandler({super.key, required this.user});

  @override
  State<AuthenticatedUserHandler> createState() =>
      _AuthenticatedUserHandlerState();
}

class _AuthenticatedUserHandlerState extends State<AuthenticatedUserHandler> {
  @override
  Widget build(BuildContext context) {
    // First check if email verification is required
    if (EmailVerificationService.needsEmailVerification()) {
      return const EmailVerificationPage();
    }

    return FutureBuilder<models.User?>(
      future: _checkUserOnboardingStatus(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    AppIcons.error,
                    size: AppDimensions.iconXxl,
                    color: AppColors.error,
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  Text('Error: ${snapshot.error}'),
                  SizedBox(height: AppDimensions.spacingM),
                  ElevatedButton(
                    onPressed: () => setState(() {}),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        final user = snapshot.data;

        // If user doesn't exist in Firestore or is not onboarded, show onboarding
        if (user == null || !user.isOnboarded) {
          return const OnboardingPage();
        }

        // User exists and is onboarded, show main app
        return const MainNavigationPage();
      },
    );
  }

  Future<models.User?> _checkUserOnboardingStatus() async {
    try {
      // Set user identifier for Crashlytics
      await LoggingService.instance.setUserIdentifier(widget.user.uid);

      // Set user ID for Analytics
      await AnalyticsService.instance.setUserId(widget.user.uid);

      // Set user ID for RevenueCat subscription service
      try {
        if (SubscriptionService.instance.isInitialized) {
          await SubscriptionService.instance.setUserId(widget.user.uid);
        }
      } catch (e) {
        // Log error but don't fail login if subscription service fails
        await LoggingService.instance.logError(
          e,
          StackTrace.current,
          'AuthenticatedUserHandler',
          'Failed to set RevenueCat user ID',
        );
      }

      // Set additional custom keys for crash reports
      await LoggingService.instance.setCustomKey(
        'user_email',
        widget.user.email ?? 'unknown',
      );
      await LoggingService.instance.setCustomKey(
        'user_display_name',
        widget.user.displayName ?? 'unknown',
      );

      // First, try to get the user from Firestore
      final firestoreUser = await FirestoreService.getUser(widget.user.uid);

      if (firestoreUser == null) {
        // User doesn't exist in Firestore, create them
        final newUser = models.User(
          id: widget.user.uid,
          name:
              widget.user.displayName ??
              widget.user.email?.split('@').first ??
              'User',
          email: widget.user.email ?? '',
          isOnboarded: false,
          isAdmin: false, // Default to non-admin
          description: null,
          preferredPersonaIds: const [],
          createdAt: DateTime.now(),
        );

        await FirestoreService.createOrUpdateUser(newUser);

        // Set additional custom keys for new user
        await LoggingService.instance.setCustomKey('user_type', 'new_user');
        await LoggingService.instance.setCustomKey('is_onboarded', false);

        // Track new user signup in analytics
        await AnalyticsService.instance.logSignUp(
          method: widget.user.providerData.isNotEmpty
              ? widget.user.providerData.first.providerId
              : 'unknown',
        );

        // Set user properties for analytics
        await AnalyticsService.instance.setUserProperty(
          name: 'user_type',
          value: 'new_user',
        );
        await AnalyticsService.instance.setUserProperty(
          name: 'is_onboarded',
          value: 'false',
        );

        // Send verification email for new email/password users
        if (EmailVerificationService.needsEmailVerification() &&
            EmailVerificationService.isRecentRegistration()) {
          try {
            await EmailVerificationService.sendEmailVerification();
          } catch (e) {
            // Log error but don't fail user creation
            debugPrint('Failed to send verification email for new user: $e');
          }
        }

        return newUser;
      }

      // Set additional custom keys for existing user
      await LoggingService.instance.setCustomKey('user_type', 'existing_user');
      await LoggingService.instance.setCustomKey(
        'is_onboarded',
        firestoreUser.isOnboarded,
      );
      await LoggingService.instance.setCustomKey(
        'is_admin',
        firestoreUser.isAdmin,
      );

      // Track existing user login in analytics
      await AnalyticsService.instance.logLogin(
        method: widget.user.providerData.isNotEmpty
            ? widget.user.providerData.first.providerId
            : 'unknown',
      );

      // Set user properties for analytics
      await AnalyticsService.instance.setUserProperty(
        name: 'user_type',
        value: 'existing_user',
      );
      await AnalyticsService.instance.setUserProperty(
        name: 'is_onboarded',
        value: firestoreUser.isOnboarded.toString(),
      );
      await AnalyticsService.instance.setUserProperty(
        name: 'is_admin',
        value: firestoreUser.isAdmin.toString(),
      );

      return firestoreUser;
    } catch (e) {
      throw Exception('Failed to check user status: $e');
    }
  }
}

class FirebaseUIAuthScreen extends StatelessWidget {
  const FirebaseUIAuthScreen({super.key});

  /// Check if Apple Sign-In should be shown (iOS native only, not web)
  bool _shouldShowAppleSignIn(BuildContext context) {
    // On web, kIsWeb is true and we should not show Apple Sign-In
    if (kIsWeb) return false;

    // On native platforms, check if it's iOS
    return Theme.of(context).platform == TargetPlatform.iOS;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: AppDimensions.paddingL,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight:
                  MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom -
                  (AppDimensions.spacingL * 2),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Welcome text
                Center(
                  child: Text(
                    'Every login is a step toward your best self.',
                    style: AppTypography.textTheme.headlineSmall,
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingXl),

                // Email authentication form
                _UnifiedEmailAuthForm(),

                // Significant spacing between email form and Google button
                SizedBox(height: AppDimensions.spacingXxl),

                // Divider with "OR" text
                Row(
                  children: [
                    Expanded(child: Divider(color: AppColors.gray300)),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.spacingM,
                      ),
                      child: Text(
                        'OR',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: AppColors.gray500,
                        ),
                      ),
                    ),
                    Expanded(child: Divider(color: AppColors.gray300)),
                  ],
                ),

                // More spacing after divider
                SizedBox(height: AppDimensions.spacingXl),

                // Apple Sign-In button (iOS only)
                if (_shouldShowAppleSignIn(context)) ...[
                  _AppleSignInButton(),
                  SizedBox(height: AppDimensions.spacingM),
                ],

                // Google Sign-In button
                FirebaseUIActions(
                  actions: [
                    AuthStateChangeAction<SignedIn>((context, state) {
                      // Handle successful sign-in - this will be handled by the parent widget
                    }),
                  ],
                  child: OAuthProviderButton(
                    provider: GoogleProvider(
                      clientId:
                          '49879574938-k2mk9104v7uo81elbfrn5e71qoa1bm3p.apps.googleusercontent.com',
                    ),
                  ),
                ),

                // Footer spacing and terms
                SizedBox(height: AppDimensions.spacingXl),
                _LegalFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Unified email authentication form that handles both login and registration
class _UnifiedEmailAuthForm extends StatefulWidget {
  @override
  State<_UnifiedEmailAuthForm> createState() => _UnifiedEmailAuthFormState();
}

class _UnifiedEmailAuthFormState extends State<_UnifiedEmailAuthForm> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  String? _errorMessage;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text;

      // First try to sign in
      try {
        await FirebaseAuth.instance.signInWithEmailAndPassword(
          email: email,
          password: password,
        );
      } on FirebaseAuthException catch (e) {
        // If user not found, create new account
        if (e.code == 'user-not-found' || e.code == 'invalid-credential') {
          try {
            final userCredential = await FirebaseAuth.instance
                .createUserWithEmailAndPassword(
                  email: email,
                  password: password,
                );

            // For new users, navigate to email verification if needed
            if (userCredential.user != null &&
                !userCredential.user!.emailVerified) {
              // The parent widget will handle navigation to email verification
            }
          } on FirebaseAuthException {
            rethrow;
          }
        } else {
          rethrow;
        }
      }
    } on FirebaseAuthException catch (e) {
      setState(() {
        _errorMessage = _getErrorMessage(e.code);
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'An unexpected error occurred. Please try again.';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _getErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'wrong-password':
      case 'invalid-credential':
        return 'Invalid email or password. Please try again.';
      case 'user-disabled':
        return 'This account has been disabled. Please contact support.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      default:
        return 'Authentication failed. Please try again.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Email field
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.next,
            decoration: const InputDecoration(
              labelText: 'Email',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.email_outlined),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter your email';
              }
              if (!RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              ).hasMatch(value.trim())) {
                return 'Please enter a valid email address';
              }
              return null;
            },
          ),
          SizedBox(height: AppDimensions.spacingM),

          // Password field
          TextFormField(
            controller: _passwordController,
            obscureText: !_isPasswordVisible,
            textInputAction: TextInputAction.done,
            onFieldSubmitted: (_) => _submitForm(),
            decoration: InputDecoration(
              labelText: 'Password',
              border: const OutlineInputBorder(),
              prefixIcon: const Icon(Icons.lock_outline),
              suffixIcon: IconButton(
                icon: Icon(
                  _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                ),
                onPressed: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your password';
              }
              if (value.length < 6) {
                return 'Password must be at least 6 characters';
              }
              return null;
            },
          ),
          SizedBox(height: AppDimensions.spacingM),

          // Error message
          if (_errorMessage != null)
            Container(
              padding: AppDimensions.paddingM,
              margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.1),
                borderRadius: AppDimensions.borderRadiusM,
                border: Border.all(
                  color: AppColors.error.withValues(alpha: 0.3),
                ),
              ),
              child: SelectableText.rich(
                TextSpan(
                  children: [
                    WidgetSpan(
                      child: Icon(
                        Icons.error_outline,
                        color: AppColors.error,
                        size: 16,
                      ),
                    ),
                    const TextSpan(text: ' '),
                    TextSpan(
                      text: _errorMessage!,
                      style: TextStyle(color: AppColors.error),
                    ),
                  ],
                ),
              ),
            ),

          // Submit button
          SizedBox(
            height: 48,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _submitForm,
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Log in or sign up'),
            ),
          ),
        ],
      ),
    );
  }
}

/// Custom Apple Sign-In button widget
class _AppleSignInButton extends StatefulWidget {
  @override
  State<_AppleSignInButton> createState() => _AppleSignInButtonState();
}

class _AppleSignInButtonState extends State<_AppleSignInButton> {
  bool _isLoading = false;

  Future<void> _signInWithApple() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Request Apple ID credential
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // Create OAuth credential for Firebase
      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      // Sign in to Firebase
      await FirebaseAuth.instance.signInWithCredential(oauthCredential);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Apple Sign-In failed: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: SignInWithAppleButton(
        onPressed: _isLoading ? null : _signInWithApple,
        text: 'Continue with Apple',
        height: 48,
        style: SignInWithAppleButtonStyle.black,
      ),
    );
  }
}

/// Legal footer widget with clickable Terms of Use and Privacy Policy links
class _LegalFooter extends StatelessWidget {
  // Hardcoded URLs for legal documents
  static const String _termsOfUseUrl = 'https://upshift.life/terms';
  static const String _privacyPolicyUrl = 'https://upshift.life/privacy';

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Wrap(
        alignment: WrapAlignment.center,
        children: [
          Text(
            'By signing in, you agree to our ',
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: AppColors.gray500,
            ),
            textAlign: TextAlign.center,
          ),
          GestureDetector(
            onTap: () =>
                _openLegalDocument(context, 'Terms of Use', _termsOfUseUrl),
            child: Text(
              'Terms of Use',
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: AppColors.primary,
                decoration: TextDecoration.underline,
                decorationColor: AppColors.primary,
              ),
            ),
          ),
          Text(
            ' and ',
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: AppColors.gray500,
            ),
          ),
          GestureDetector(
            onTap: () => _openLegalDocument(
              context,
              'Privacy Policy',
              _privacyPolicyUrl,
            ),
            child: Text(
              'Privacy Policy',
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: AppColors.primary,
                decoration: TextDecoration.underline,
                decorationColor: AppColors.primary,
              ),
            ),
          ),
          Text(
            '.',
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: AppColors.gray500,
            ),
          ),
        ],
      ),
    );
  }

  void _openLegalDocument(BuildContext context, String title, String url) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LegalWebViewPage(title: title, url: url),
      ),
    );
  }
}
